<!--
*Author：jxx
 *Contact：<EMAIL>
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/trialwork/trialwork/TrialWork_CheckInRecords.jsx此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/trialwork/trialwork/TrialWork_CheckInRecords.jsx";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'RecordId',
                footer: "Foots",
                cnName: '试工打卡记录',
                name: 'trialwork/TrialWork_CheckInRecords',
                url: "/TrialWork_CheckInRecords/",
                sortName: "CheckInDate,CheckInTime"
            });
            const editFormFields = ref({"UserId":"","CheckInDate":"","CheckInTime":"","Type":"","Latitude":"","Longitude":"","Address":"","IsInWorkArea":"","DeviceInfo":"","Remarks":"","Status":""});
            const editFormOptions = ref([[{"title":"打卡日期","required":true,"field":"CheckInDate"},
                               {"title":"打卡时间","required":true,"field":"CheckInTime"},
                               {"dataKey":"TrialWork_User","data":[],"title":"用户","required":true,"field":"UserId","type":"number"}],
                              [{"title":"打卡纬度","field":"Latitude","type":"decimal"},
                               {"title":"打卡经度","field":"Longitude","type":"decimal"},
                               {"dataKey":"打卡类型","data":[],"title":"打卡类型","required":true,"field":"Type"}],
                              [{"title":"打卡地址","field":"Address"},
                               {"title":"是否在工作区域内","field":"IsInWorkArea"},
                               {"title":"设备信息","field":"DeviceInfo"}],
                              [{"title":"备注","field":"Remarks"}],
                              [{"dataKey":"考勤状态","data":[],"title":"状态","field":"Status"}]]);
            const searchFormFields = ref({"UserId":"","Status":""});
            const searchFormOptions = ref([[{"dataKey":"TrialWork_User","data":[],"title":"用户","field":"UserId","type":"select"},{"dataKey":"考勤状态","data":[],"title":"状态","field":"Status","type":"select"}]]);
            const columns = ref([{field:'RecordId',title:'记录ID（主键）',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'UserId',title:'用户',type:'int',bind:{ key:'TrialWork_User',data:[]},width:110,require:true,align:'left',sort:true},
                       {field:'CheckInDate',title:'打卡日期',type:'date',width:150,require:true,align:'left'},
                       {field:'CheckInTime',title:'打卡时间',type:'datetime',width:150,require:true,align:'left',sort:true},
                       {field:'Type',title:'打卡类型',type:'string',bind:{ key:'打卡类型',data:[]},width:110,require:true,align:'left'},
                       {field:'Latitude',title:'打卡纬度',type:'decimal',width:110,hidden:true,align:'left'},
                       {field:'Longitude',title:'打卡经度',type:'decimal',width:110,hidden:true,align:'left'},
                       {field:'Address',title:'打卡地址',type:'string',width:180,align:'left'},
                       {field:'IsInWorkArea',title:'是否在工作区域内',type:'bool',width:110,hidden:true,align:'left'},
                       {field:'DeviceInfo',title:'设备信息',type:'string',width:180,hidden:true,align:'left'},
                       {field:'Remarks',title:'备注',type:'string',width:220,hidden:true,align:'left'},
                       {field:'CreateID',title:'创建人ID',type:'int',width:100,hidden:true,require:true,align:'left'},
                       {field:'Creator',title:'创建人',type:'string',width:100,hidden:true,require:true,align:'left'},
                       {field:'CreateDate',title:'创建时间',type:'datetime',width:150,hidden:true,require:true,align:'left',sort:true},
                       {field:'Status',title:'状态',type:'string',bind:{ key:'考勤状态',data:[]},width:110,align:'left'},
                       {field:'Period',title:'时间区域',type:'string',width:120,hidden:true,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
